{"feConfigs": {"lafEnv": "", "systemTitle": "Towa", "disablePublishEduStore": true, "disablePublishBenZui": true, "companyName": "况客科技（北京）有限公司", "icp": "京ICP备15006816号-1|京公网安备 11010502035044号"}, "systemEnv": {"openapiPrefix": "towa", "vectorMaxProcess": 15, "qaMaxProcess": 15, "pgHNSWEfSearch": 100}, "llmModels": [{"model": "deepseek-test", "name": "DeepseekTest", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 16000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": true, "usedInClassify": false, "usedInExtractFields": true, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}, "baseUrl": "http://114.xxx:10805/v1", "apiKey": "abcdfdfd"}, {"model": "gpt-4o-mini-2024-07-18", "name": "TOWA-4o-MINI", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 16000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": true, "usedInClassify": false, "usedInExtractFields": true, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "towa-4o-pro", "name": "TOWA-4o-Pro", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 16000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": true, "usedInClassify": false, "usedInExtractFields": true, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "gpt-4o-2024-05-13", "name": "TOWA-4o", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": true, "usedInClassify": false, "usedInExtractFields": true, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "gpt-4.5-preview-vip", "name": "TOWA-4.5", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 16000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": true, "usedInClassify": false, "usedInExtractFields": true, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "claude-3-5-sonnet-20241022", "name": "TOWA-SONNET", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": true, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "claude-3-7-sonnet-20250219", "name": "TOWA-SONNET-3.7", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": true, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "towa-gemini-2.0-flash-exp", "name": "TOWA-Gemini", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "towa-gemini-2.5-pro", "name": "TOWA-Gemini-Pro", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "deepseek-reasoner", "name": "TOWA-DS-R1", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 16000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "deepseek-ai/deepseek-r1", "name": "TOWA-NV-DS-R1", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "ep-20250205211415-6zzr6", "name": "TOWA-ARK-DS-R1", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "Pro/deepseek-ai/DeepSeek-R1", "name": "TOWA-SF-DS-R1", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "ep-20250208183104-g676t", "name": "TOWA-DS-R1-32B", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "ep-20250208174558-2tnzv", "name": "TOWA-DS-R1-7B", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "deepseek-chat", "name": "TOWA-DS3", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "towa-deepseek-ark-v3", "name": "TOWA-ARK-DS3", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "Pro/deepseek-ai/DeepSeek-V3", "name": "TOWA-SF-DS3", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "gpt-3.5-turbo", "name": "TOWA-3.5", "maxContext": 16000, "avatar": "/imgs/model/towa.svg", "maxResponse": 8000, "quoteMaxToken": 13000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "ep-20240816094956-p6kb2", "name": "TOWA-DOUBAO", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "ep-20240815225806-zmmgs", "name": "TOWA-DOUBAO-128K", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "glm-4", "name": "TOWA-GLM4", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "qwq-plus", "name": "TOWA-QWQ-PLUS", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "qwen-max", "name": "TOWA-QWEN-MAX", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "qwen-plus", "name": "TOWA-QWEN-PLUS", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "qwen-vl-max", "name": "TOWA-QWEN-VL-MAX", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "qwen2.5-72b-instruct", "name": "TOWA-QWEN2.5-72B", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}, {"model": "llama3:latest", "name": "TOWA-Llama3", "avatar": "/imgs/model/towa.svg", "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 100000, "maxTemperature": 1.2, "charsPointsPrice": 0.001, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "usedInQueryExtension": false, "toolChoice": true, "functionCall": false, "customCQPrompt": "", "customExtractPrompt": "", "defaultSystemChatPrompt": "", "defaultConfig": {}}], "vectorModels": [{"model": "embed-3L", "name": "Embedding-3L-openai", "avatar": "/imgs/model/towa.svg", "charsPointsPrice": 0.001, "defaultToken": 512, "maxToken": 3000, "weight": 100, "dbConfig": {}, "queryConfig": {}, "defaultConfig": {"dimensions": 1024}}, {"model": "embedding-2", "name": "Embedding-2-<PERSON><PERSON><PERSON>", "avatar": "/imgs/model/towa.svg", "charsPointsPrice": 0.001, "defaultToken": 512, "maxToken": 3000, "weight": 100, "dbConfig": {}, "queryConfig": {}}], "reRankModels": [], "audioSpeechModels": [{"model": "tts-1", "name": "OpenAI TTS1", "charsPointsPrice": 0.001, "voices": [{"label": "<PERSON><PERSON>", "value": "alloy", "bufferId": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "Echo", "value": "echo", "bufferId": "openai-Echo"}, {"label": "Fable", "value": "fable", "bufferId": "openai-Fable"}, {"label": "Onyx", "value": "onyx", "bufferId": "openai-Onyx"}, {"label": "Nova", "value": "nova", "bufferId": "openai-Nova"}, {"label": "Shimmer", "value": "shimmer", "bufferId": "openai-Shimmer"}]}], "whisperModel": {"model": "whisper-1", "name": "Whisper1", "charsPointsPrice": 0.001}}